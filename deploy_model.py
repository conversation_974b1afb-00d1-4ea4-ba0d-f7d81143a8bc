#!/usr/bin/env python3
"""
Model Deployment Script for Rainfall Prediction
This script demonstrates how to load and use the trained model for predictions
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
import joblib
import warnings
warnings.filterwarnings('ignore')

from data_preprocessing import handle_missing_values, encode_categorical_variables
from feature_engineering import create_weather_features

class RainfallPredictor:
    """
    Production-ready rainfall prediction model
    """
    
    def __init__(self, model_path='models/'):
        """
        Initialize the predictor with saved model components
        
        Args:
            model_path (str): Path to saved model files
        """
        self.model_path = model_path
        self.model = None
        self.scaler = None
        self.encoders = None
        self.selected_features = None
        self.is_loaded = False
        
    def load_model(self):
        """Load all model components"""
        try:
            # Load trained model
            model_file = os.path.join(self.model_path, 'best_rainfall_model.pkl')
            if os.path.exists(model_file):
                self.model = joblib.load(model_file)
                print("✅ Model loaded successfully")
            else:
                print("❌ Model file not found. Please train the model first.")
                return False
            
            # Load preprocessing components
            scaler_file = os.path.join(self.model_path, 'feature_scaler.pkl')
            if os.path.exists(scaler_file):
                self.scaler = joblib.load(scaler_file)
                print("✅ Feature scaler loaded")
            
            encoders_file = os.path.join(self.model_path, 'categorical_encoders.pkl')
            if os.path.exists(encoders_file):
                self.encoders = joblib.load(encoders_file)
                print("✅ Categorical encoders loaded")
            
            features_file = os.path.join(self.model_path, 'selected_features.pkl')
            if os.path.exists(features_file):
                self.selected_features = joblib.load(features_file)
                print("✅ Selected features loaded")
            
            self.is_loaded = True
            return True
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def preprocess_data(self, data):
        """
        Preprocess input data for prediction
        
        Args:
            data (pd.DataFrame): Raw weather data
            
        Returns:
            pd.DataFrame: Preprocessed data ready for prediction
        """
        if not self.is_loaded:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        # Handle missing values
        data_cleaned = handle_missing_values(data, strategy='auto')
        
        # Encode categorical variables (excluding target)
        data_encoded, _ = encode_categorical_variables(data_cleaned, target_column=None)
        
        # Feature engineering
        data_features = create_weather_features(data_encoded)
        
        # Select only the features used during training
        if self.selected_features:
            # Ensure all required features are present
            missing_features = set(self.selected_features) - set(data_features.columns)
            if missing_features:
                print(f"⚠️ Missing features: {missing_features}")
                # Add missing features with default values
                for feature in missing_features:
                    data_features[feature] = 0
            
            data_final = data_features[self.selected_features]
        else:
            data_final = data_features
        
        # Scale features
        if self.scaler:
            data_scaled = self.scaler.transform(data_final)
            data_final = pd.DataFrame(data_scaled, columns=data_final.columns, index=data_final.index)
        
        return data_final
    
    def predict(self, data):
        """
        Make rainfall predictions
        
        Args:
            data (pd.DataFrame): Weather data for prediction
            
        Returns:
            tuple: (predictions, probabilities)
        """
        if not self.is_loaded:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        # Preprocess data
        processed_data = self.preprocess_data(data)
        
        # Make predictions
        predictions = self.model.predict(processed_data)
        probabilities = None
        
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(processed_data)
        
        return predictions, probabilities
    
    def predict_single(self, weather_data):
        """
        Predict rainfall for a single weather observation
        
        Args:
            weather_data (dict): Dictionary with weather variables
            
        Returns:
            dict: Prediction result with confidence
        """
        # Convert to DataFrame
        df = pd.DataFrame([weather_data])
        
        # Make prediction
        predictions, probabilities = self.predict(df)
        
        result = {
            'prediction': 'Rain' if predictions[0] == 1 else 'No Rain',
            'confidence': float(np.max(probabilities[0])) if probabilities is not None else None,
            'rain_probability': float(probabilities[0][1]) if probabilities is not None else None
        }
        
        return result

def create_sample_prediction():
    """Demonstrate model usage with sample data"""
    
    print("🌧️ RAINFALL PREDICTION DEMO")
    print("=" * 40)
    
    # Initialize predictor
    predictor = RainfallPredictor()
    
    # Load model
    if not predictor.load_model():
        print("❌ Failed to load model. Please run the training pipeline first.")
        return
    
    # Sample weather data
    sample_weather = {
        'Date': '2024-01-15',
        'Location': 'Sydney',
        'MinTemp': 18.5,
        'MaxTemp': 28.3,
        'Rainfall': 0.0,
        'Evaporation': 4.2,
        'Sunshine': 9.1,
        'WindGustDir': 'NW',
        'WindGustSpeed': 35.0,
        'WindDir9am': 'N',
        'WindDir3pm': 'NW',
        'WindSpeed9am': 12.0,
        'WindSpeed3pm': 18.0,
        'Humidity9am': 65.0,
        'Humidity3pm': 45.0,
        'Pressure9am': 1018.2,
        'Pressure3pm': 1015.8,
        'Cloud9am': 3,
        'Cloud3pm': 5,
        'Temp9am': 20.1,
        'Temp3pm': 26.7,
        'RainToday': 'No'
    }
    
    print("\n📊 Sample Weather Data:")
    print("-" * 25)
    for key, value in sample_weather.items():
        print(f"{key}: {value}")
    
    # Make prediction
    try:
        result = predictor.predict_single(sample_weather)
        
        print(f"\n🎯 PREDICTION RESULTS:")
        print("-" * 25)
        print(f"Prediction: {result['prediction']}")
        if result['confidence']:
            print(f"Confidence: {result['confidence']:.1%}")
        if result['rain_probability']:
            print(f"Rain Probability: {result['rain_probability']:.1%}")
        
        # Interpretation
        if result['rain_probability']:
            if result['rain_probability'] > 0.7:
                print("🌧️ High chance of rain - prepare for wet weather!")
            elif result['rain_probability'] > 0.4:
                print("⛅ Moderate chance of rain - keep an umbrella handy")
            else:
                print("☀️ Low chance of rain - expect clear weather")
        
    except Exception as e:
        print(f"❌ Prediction failed: {e}")

def batch_prediction_example():
    """Example of batch predictions"""
    
    print("\n📊 BATCH PREDICTION EXAMPLE")
    print("=" * 40)
    
    # Load sample data
    try:
        df = pd.read_csv('data/weather_data.csv')
        sample_data = df.head(5).copy()
        
        # Remove target column if present
        if 'RainTomorrow' in sample_data.columns:
            sample_data = sample_data.drop('RainTomorrow', axis=1)
        
        predictor = RainfallPredictor()
        if predictor.load_model():
            predictions, probabilities = predictor.predict(sample_data)
            
            print(f"\nPredictions for {len(predictions)} samples:")
            for i, (pred, prob) in enumerate(zip(predictions, probabilities)):
                rain_prob = prob[1] if prob is not None else 0
                result = "Rain" if pred == 1 else "No Rain"
                print(f"Sample {i+1}: {result} (Probability: {rain_prob:.1%})")
        
    except Exception as e:
        print(f"❌ Batch prediction failed: {e}")

if __name__ == "__main__":
    # Run demonstration
    create_sample_prediction()
    batch_prediction_example()
    
    print("\n" + "=" * 50)
    print("🎉 Model deployment demonstration completed!")
    print("=" * 50)
    
    print("\n💡 Usage Tips:")
    print("1. Ensure all required weather variables are provided")
    print("2. Handle missing values appropriately")
    print("3. Monitor model performance over time")
    print("4. Retrain periodically with new data")
    print("5. Set up alerts for high-confidence predictions")
