{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Rainfall Prediction Machine Learning Project\n", "\n", "## Project Overview\n", "This project develops a binary classification model to predict whether it will rain tomorrow based on historical weather data. We'll act as data scientists at WeatherTech Inc. to build a robust ML pipeline.\n", "\n", "### Objectives:\n", "- Build a binary classifier to predict rainfall (rain/no rain tomorrow)\n", "- Perform comprehensive data exploration and preprocessing\n", "- Compare multiple machine learning algorithms\n", "- Provide actionable insights and model interpretation\n", "\n", "### Dataset:\n", "Australian Weather Dataset containing meteorological variables such as:\n", "- Temperature (min, max, current)\n", "- Humidity levels\n", "- Wind speed and direction\n", "- Atmospheric pressure\n", "- Cloud cover\n", "- Previous day rainfall\n", "- Target: <PERSON><PERSON><PERSON><PERSON> (Yes/No)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import custom modules\n", "import sys\n", "sys.path.append('../src')\n", "\n", "from data_preprocessing import load_data, basic_info, detect_outliers, handle_missing_values\n", "from feature_engineering import create_weather_features, prepare_final_features\n", "from model_training import split_data, train_logistic_regression, train_random_forest, train_xgboost\n", "from model_training import evaluate_model, compare_models, hyperparameter_tuning_rf\n", "from visualization import plot_data_overview, plot_target_distribution, plot_correlation_matrix\n", "from visualization import plot_confusion_matrix, plot_roc_curve, plot_feature_importance\n", "\n", "# Set display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download and Load Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download Australian Weather Dataset\n", "import urllib.request\n", "import os\n", "\n", "# Dataset URL (Australian Weather Data)\n", "dataset_url = \"https://raw.githubusercontent.com/rfordatascience/tidytuesday/master/data/2020/2020-01-07/rainfall.csv\"\n", "dataset_path = \"../data/weather_data.csv\"\n", "\n", "# Download if not exists\n", "if not os.path.exists(dataset_path):\n", "    print(\"Downloading dataset...\")\n", "    try:\n", "        urllib.request.urlretrieve(dataset_url, dataset_path)\n", "        print(f\"Dataset downloaded successfully to {dataset_path}\")\n", "    except Exception as e:\n", "        print(f\"Error downloading dataset: {e}\")\n", "        print(\"Please manually download a weather dataset and place it in the data folder.\")\n", "else:\n", "    print(\"Dataset already exists.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Alternative: Create a synthetic weather dataset for demonstration\n", "# This will be used if the download fails\n", "\n", "def create_synthetic_weather_data(n_samples=5000):\n", "    \"\"\"\n", "    Create a synthetic weather dataset for rainfall prediction\n", "    \"\"\"\n", "    np.random.seed(42)\n", "    \n", "    # Generate synthetic weather data\n", "    data = {\n", "        'Date': pd.date_range('2020-01-01', periods=n_samples, freq='D'),\n", "        'Location': np.random.choice(['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide'], n_samples),\n", "        'MinTemp': np.random.normal(15, 8, n_samples),\n", "        'MaxTemp': np.random.normal(25, 10, n_samples),\n", "        'Rainfall': np.random.exponential(2, n_samples),\n", "        'Evaporation': np.random.normal(5, 2, n_samples),\n", "        'Sunshine': np.random.normal(8, 3, n_samples),\n", "        'WindGustDir': np.random.choice(['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'], n_samples),\n", "        'WindGustSpeed': np.random.normal(30, 15, n_samples),\n", "        'WindDir9am': np.random.choice(['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'], n_samples),\n", "        'WindDir3pm': np.random.choice(['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'], n_samples),\n", "        'WindSpeed9am': np.random.normal(15, 8, n_samples),\n", "        'WindSpeed3pm': np.random.normal(18, 10, n_samples),\n", "        'Humidity9am': np.random.normal(70, 20, n_samples),\n", "        'Humidity3pm': np.random.normal(50, 25, n_samples),\n", "        'Pressure9am': np.random.normal(1015, 10, n_samples),\n", "        'Pressure3pm': np.random.normal(1013, 10, n_samples),\n", "        'Cloud9am': np.random.randint(0, 9, n_samples),\n", "        'Cloud3pm': np.random.randint(0, 9, n_samples),\n", "        'Temp9am': np.random.normal(18, 8, n_samples),\n", "        'Temp3pm': np.random.normal(22, 10, n_samples),\n", "        'RainToday': np.random.choice(['Yes', 'No'], n_samples, p=[0.3, 0.7])\n", "    }\n", "    \n", "    df = pd.DataFrame(data)\n", "    \n", "    # Create target variable with some logic\n", "    # Higher chance of rain if:\n", "    # - High humidity\n", "    # - Low pressure\n", "    # - High cloud cover\n", "    # - Rained today\n", "    \n", "    rain_prob = (\n", "        (df['Humidity3pm'] / 100) * 0.3 +\n", "        ((1020 - df['Pressure3pm']) / 20) * 0.2 +\n", "        (df['Cloud3pm'] / 8) * 0.2 +\n", "        (df['RainToday'] == 'Yes').astype(int) * 0.3\n", "    )\n", "    \n", "    df['RainTomorrow'] = np.random.binomial(1, rain_prob, n_samples)\n", "    df['RainTomorrow'] = df['RainTomorrow'].map({1: 'Yes', 0: 'No'})\n", "    \n", "    # Add some missing values to make it realistic\n", "    missing_cols = ['Evaporation', '<PERSON>', 'WindGustSpeed', 'WindSpeed9am', 'WindSpeed3pm']\n", "    for col in missing_cols:\n", "        missing_idx = np.random.choice(df.index, size=int(0.1 * len(df)), replace=False)\n", "        df.loc[missing_idx, col] = np.nan\n", "    \n", "    return df\n", "\n", "# Try to load the dataset, create synthetic if needed\n", "try:\n", "    if os.path.exists(dataset_path):\n", "        df = pd.read_csv(dataset_path)\n", "        print(f\"Loaded dataset from {dataset_path}\")\n", "    else:\n", "        raise FileNotFoundError(\"Dataset not found\")\n", "except:\n", "    print(\"Creating synthetic weather dataset for demonstration...\")\n", "    df = create_synthetic_weather_data(5000)\n", "    df.to_csv(dataset_path, index=False)\n", "    print(f\"Synthetic dataset created and saved to {dataset_path}\")\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(\"\\nFirst few rows:\")\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Initial Data Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic dataset information\n", "basic_info(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic statistics\n", "print(\"=== DATASET STATISTICS ===\")\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check target variable distribution\n", "if 'RainTomorrow' in df.columns:\n", "    print(\"Target Variable Distribution:\")\n", "    print(df['RainTomorrow'].value_counts())\n", "    print(\"\\nTarget Variable Proportions:\")\n", "    print(df['RainTomorrow'].value_counts(normalize=True))\n", "    \n", "    # Plot target distribution\n", "    plot_target_distribution(df['RainTomorrow'], \"RainTomorrow Distribution\")\n", "else:\n", "    print(\"Target variable 'RainTomorrow' not found in dataset\")\n", "    print(\"Available columns:\", df.columns.tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data overview visualization\n", "plot_data_overview(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for duplicates\n", "duplicates = df.duplicated().sum()\n", "print(f\"Number of duplicate rows: {duplicates}\")\n", "\n", "if duplicates > 0:\n", "    print(\"Removing duplicate rows...\")\n", "    df = df.drop_duplicates()\n", "    print(f\"Dataset shape after removing duplicates: {df.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Outlier detection for numerical columns\n", "numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "print(f\"Numerical columns: {numerical_cols}\")\n", "\n", "if numerical_cols:\n", "    outliers_info = detect_outliers(df, numerical_cols, method='iqr')\n", "    \n", "    print(\"\\n=== OUTLIER DETECTION RESULTS ===\")\n", "    for col, info in outliers_info.items():\n", "        if info['count'] > 0:\n", "            print(f\"{col}: {info['count']} outliers ({info['percentage']:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Exploratory Data Analysis (EDA)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Correlation analysis\n", "if len(numerical_cols) > 1:\n", "    plot_correlation_matrix(df[numerical_cols], figsize=(12, 10))\n", "else:\n", "    print(\"Not enough numerical columns for correlation analysis\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Distribution of key features\n", "key_features = ['MinTemp', 'MaxTemp', 'Rainfall', 'Humidity9am', 'Humidity3pm', 'Pressure9am', 'Pressure3pm']\n", "available_features = [col for col in key_features if col in df.columns]\n", "\n", "if available_features:\n", "    from visualization import plot_feature_distributions\n", "    plot_feature_distributions(df, available_features, figsize=(15, 10))\n", "else:\n", "    print(\"Key weather features not found in dataset\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary of Initial Exploration\n", "\n", "Based on the initial exploration, we have:\n", "\n", "1. **Dataset Overview**: Loaded weather dataset with meteorological variables\n", "2. **Data Quality**: Assessed missing values, duplicates, and outliers\n", "3. **Target Variable**: Analyzed distribution of RainTomorrow (binary classification)\n", "4. **Feature Analysis**: Examined correlations and distributions of key weather variables\n", "\n", "### Next Steps:\n", "1. Data cleaning and preprocessing\n", "2. Feature engineering\n", "3. Model development and training\n", "4. Model evaluation and comparison\n", "5. Feature importance analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Preprocessing and Cleaning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle missing values\n", "print(\"=== HANDLING MISSING VALUES ===\")\n", "df_cleaned = handle_missing_values(df, strategy='auto')\n", "print(f\"\\nDataset shape after handling missing values: {df_cleaned.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Encode categorical variables\n", "from data_preprocessing import encode_categorical_variables\n", "\n", "print(\"=== ENCODING CATEGORICAL VARIABLES ===\")\n", "df_encoded, encoders = encode_categorical_variables(df_cleaned, target_column='RainTomorrow')\n", "print(f\"Dataset shape after encoding: {df_encoded.shape}\")\n", "print(f\"Encoders created: {list(encoders.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Feature Engineering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create weather-specific features\n", "print(\"=== FEATURE ENGINEERING ===\")\n", "df_features = create_weather_features(df_encoded)\n", "print(f\"Dataset shape after feature engineering: {df_features.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare final feature set\n", "print(\"=== PREPARING FINAL FEATURES ===\")\n", "X, y, selected_features = prepare_final_features(\n", "    df_features, \n", "    target_column='RainTomorrow', \n", "    feature_selection_method='auto',\n", "    n_features=20\n", ")\n", "\n", "print(f\"Final feature matrix shape: {X.shape}\")\n", "print(f\"Target variable shape: {y.shape}\")\n", "print(f\"Selected features: {selected_features}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Model Development and Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data into training and testing sets\n", "print(\"=== DATA SPLITTING ===\")\n", "X_train, X_test, y_train, y_test = split_data(X, y, test_size=0.2, random_state=42)\n", "\n", "# Scale features\n", "from data_preprocessing import scale_features\n", "X_train_scaled, X_test_scaled, scaler = scale_features(X_train, X_test, method='standard')\n", "print(\"Features scaled successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle class imbalance if needed\n", "from model_training import handle_class_imbalance\n", "\n", "print(\"=== HANDLING CLASS IMBALANCE ===\")\n", "X_train_balanced, y_train_balanced = handle_class_imbalance(\n", "    X_train_scaled, y_train, method='smote'\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train multiple models\n", "print(\"=== TRAINING MODELS ===\")\n", "\n", "# Logistic Regression\n", "lr_model = train_logistic_regression(X_train_balanced, y_train_balanced)\n", "\n", "# Random Forest\n", "rf_model = train_random_forest(X_train_balanced, y_train_balanced)\n", "\n", "# XGBoost\n", "xgb_model = train_xgboost(X_train_balanced, y_train_balanced)\n", "\n", "print(\"All models trained successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Model Evaluation and Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate all models\n", "print(\"=== MODEL EVALUATION ===\")\n", "\n", "models = {\n", "    'Logistic Regression': lr_model,\n", "    'Random Forest': rf_model,\n", "    'XGBoost': xgb_model\n", "}\n", "\n", "# Compare models\n", "results_df = compare_models(models, X_test_scaled, y_test)\n", "\n", "# Plot model comparison\n", "from visualization import plot_model_comparison\n", "plot_model_comparison(results_df, metric='accuracy')\n", "plot_model_comparison(results_df, metric='f1_score')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detailed evaluation of the best model\n", "best_model_name = results_df.iloc[0]['model_name']\n", "best_model = models[best_model_name]\n", "\n", "print(f\"\\n=== DETAILED EVALUATION OF {best_model_name.upper()} ===\")\n", "\n", "# Predictions\n", "y_pred = best_model.predict(X_test_scaled)\n", "y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1] if hasattr(best_model, 'predict_proba') else None\n", "\n", "# Confusion Matrix\n", "plot_confusion_matrix(y_test, y_pred, labels=['No Rain', 'Rain'], title=f'{best_model_name} - Confusion Matrix')\n", "\n", "# ROC Curve\n", "if y_pred_proba is not None:\n", "    plot_roc_curve(y_test, y_pred_proba, title=f'{best_model_name} - ROC Curve')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Feature Importance Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature importance analysis\n", "print(\"=== FEATURE IMPORTANCE ANALYSIS ===\")\n", "\n", "if hasattr(best_model, 'feature_importances_'):\n", "    # For tree-based models\n", "    feature_importance = pd.DataFrame({\n", "        'Feature': selected_features,\n", "        'Importance': best_model.feature_importances_\n", "    }).sort_values('Importance', ascending=False)\n", "    \n", "    print(\"Top 10 Most Important Features:\")\n", "    print(feature_importance.head(10))\n", "    \n", "    # Plot feature importance\n", "    plot_feature_importance(feature_importance, top_n=15, title=f'{best_model_name} - Feature Importance')\n", "    \n", "<PERSON><PERSON>(best_model, 'coef_'):\n", "    # For linear models\n", "    feature_importance = pd.DataFrame({\n", "        'Feature': selected_features,\n", "        'Importance': np.abs(best_model.coef_[0])\n", "    }).sort_values('Importance', ascending=False)\n", "    \n", "    print(\"Top 10 Most Important Features:\")\n", "    print(feature_importance.head(10))\n", "    \n", "    # Plot feature importance\n", "    plot_feature_importance(feature_importance, top_n=15, title=f'{best_model_name} - Feature Importance')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Hyperparameter Tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Hyperparameter tuning for the best model\n", "print(\"=== HYPERPARAMETER TUNING ===\")\n", "\n", "if best_model_name == 'Random Forest':\n", "    tuned_model = hyperparameter_tuning_rf(X_train_balanced, y_train_balanced, cv=3, n_iter=10)\n", "elif best_model_name == 'XGBoost':\n", "    from model_training import hyperparameter_tuning_xgb\n", "    tuned_model = hyperparameter_tuning_xgb(X_train_balanced, y_train_balanced, cv=3, n_iter=10)\n", "else:\n", "    tuned_model = best_model\n", "    print(f\"No hyperparameter tuning implemented for {best_model_name}\")\n", "\n", "# Evaluate tuned model\n", "if tuned_model != best_model:\n", "    tuned_metrics = evaluate_model(tuned_model, X_test_scaled, y_test, f\"Tuned {best_model_name}\")\n", "    print(f\"\\nImprovement in accuracy: {tuned_metrics['accuracy'] - results_df.iloc[0]['accuracy']:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Model Interpretation and Business Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Business insights and interpretation\n", "print(\"=== BUSINESS INSIGHTS ===\")\n", "print(\"\\n🌧️ RAINFALL PREDICTION MODEL INSIGHTS:\")\n", "print(\"=\"*50)\n", "\n", "# Model performance summary\n", "best_accuracy = results_df.iloc[0]['accuracy']\n", "best_f1 = results_df.iloc[0]['f1_score']\n", "\n", "print(f\"📊 Best Model: {best_model_name}\")\n", "print(f\"🎯 Accuracy: {best_accuracy:.3f} ({best_accuracy*100:.1f}%)\")\n", "print(f\"⚖️ F1-Score: {best_f1:.3f}\")\n", "\n", "# Feature insights\n", "if 'feature_importance' in locals():\n", "    top_3_features = feature_importance.head(3)['Feature'].tolist()\n", "    print(f\"\\n🔍 Top 3 Predictive Features:\")\n", "    for i, feature in enumerate(top_3_features, 1):\n", "        print(f\"   {i}. {feature}\")\n", "\n", "# Practical recommendations\n", "print(f\"\\n💡 RECOMMENDATIONS FOR WEATHERTECH INC:\")\n", "print(\"=\"*50)\n", "print(\"1. 📈 Deploy this model for daily rainfall predictions\")\n", "print(\"2. 🎯 Focus on collecting high-quality data for top predictive features\")\n", "print(\"3. 🔄 Retrain model monthly with new data to maintain accuracy\")\n", "print(\"4. ⚠️ Set up alerts for high-confidence rain predictions\")\n", "print(\"5. 📊 Monitor model performance and drift over time\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Model Saving and Deployment Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the best model and preprocessing components\n", "from model_training import save_model\n", "import joblib\n", "\n", "print(\"=== SAVING MODEL AND COMPONENTS ===\")\n", "\n", "# Save the best model\n", "model_to_save = tuned_model if 'tuned_model' in locals() and tuned_model != best_model else best_model\n", "save_model(model_to_save, '../models/best_rainfall_model.pkl')\n", "\n", "# Save preprocessing components\n", "joblib.dump(scaler, '../models/feature_scaler.pkl')\n", "joblib.dump(encoders, '../models/categorical_encoders.pkl')\n", "joblib.dump(selected_features, '../models/selected_features.pkl')\n", "\n", "print(\"Model and preprocessing components saved successfully!\")\n", "print(\"\\nFiles saved:\")\n", "print(\"- ../models/best_rainfall_model.pkl\")\n", "print(\"- ../models/feature_scaler.pkl\")\n", "print(\"- ../models/categorical_encoders.pkl\")\n", "print(\"- ../models/selected_features.pkl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Project Summary and Conclusions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final project summary\n", "print(\"=\" * 60)\n", "print(\"🎉 RAINFALL PREDICTION PROJECT COMPLETED SUCCESSFULLY!\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n📋 PROJECT SUMMARY:\")\n", "print(f\"   • Dataset Size: {df.shape[0]:,} samples, {df.shape[1]} original features\")\n", "print(f\"   • Final Features: {len(selected_features)} selected features\")\n", "print(f\"   • Best Model: {best_model_name}\")\n", "print(f\"   • Model Accuracy: {best_accuracy:.3f} ({best_accuracy*100:.1f}%)\")\n", "print(f\"   • F1-Score: {best_f1:.3f}\")\n", "\n", "print(f\"\\n🔬 METHODOLOGY APPLIED:\")\n", "print(\"   ✅ Comprehensive Exploratory Data Analysis\")\n", "print(\"   ✅ Data Cleaning and Preprocessing\")\n", "print(\"   ✅ Feature Engineering and Selection\")\n", "print(\"   ✅ Multiple Model Training and Comparison\")\n", "print(\"   ✅ Hyperparameter Tuning\")\n", "print(\"   ✅ Model Evaluation and Interpretation\")\n", "print(\"   ✅ Business Insights and Recommendations\")\n", "\n", "print(f\"\\n🚀 NEXT STEPS:\")\n", "print(\"   1. Deploy model to production environment\")\n", "print(\"   2. Set up automated retraining pipeline\")\n", "print(\"   3. Create monitoring dashboard\")\n", "print(\"   4. Collect user feedback and iterate\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"Thank you for using the Rainfall Prediction ML Pipeline!\")\n", "print(\"=\" * 60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}