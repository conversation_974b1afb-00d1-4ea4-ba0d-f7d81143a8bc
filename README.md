# Rainfall Prediction ML Project

## Project Overview
This project develops a binary classification model to predict whether it will rain tomorrow based on historical weather data. The project simulates the role of a data scientist at WeatherTech Inc.

## Objective
Build a robust machine learning pipeline that can accurately predict rainfall (rain/no rain tomorrow) using meteorological variables such as temperature, humidity, wind speed, and other weather features.

## Project Structure
```
ML_project/
├── data/                          # Dataset storage
├── notebooks/                     # Jupyter notebooks
│   └── rainfall_prediction.ipynb  # Main analysis notebook
├── src/                          # Source code modules
│   ├── __init__.py
│   ├── data_preprocessing.py     # Data cleaning and preprocessing
│   ├── feature_engineering.py   # Feature creation and selection
│   ├── model_training.py        # Model training and evaluation
│   └── visualization.py         # Plotting and visualization functions
├── models/                       # Trained model storage
├── results/                      # Output results and plots
├── requirements.txt              # Python dependencies
└── README.md                     # Project documentation
```

## Dataset
The project uses historical weather data containing features such as:
- Temperature (min, max, current)
- Humidity levels
- Wind speed and direction
- Atmospheric pressure
- Cloud cover
- Previous day rainfall
- Target variable: RainTomorrow (Yes/No)

## Methodology
1. **Data Exploration**: Comprehensive EDA to understand data patterns
2. **Data Preprocessing**: Handle missing values, outliers, and data cleaning
3. **Feature Engineering**: Create meaningful features and encode variables
4. **Model Development**: Train multiple classification algorithms
5. **Model Evaluation**: Compare performance using various metrics
6. **Model Interpretation**: Analyze feature importance and business insights

## Models Implemented
- Logistic Regression
- Random Forest Classifier
- XGBoost Classifier
- Additional models as needed

## Evaluation Metrics
- Accuracy
- Precision, Recall, F1-Score
- ROC-AUC
- Confusion Matrix
- Feature Importance Analysis

## Setup Instructions
1. Clone the repository
2. Create virtual environment: `python -m venv venv`
3. Activate virtual environment: `venv\Scripts\activate` (Windows) or `source venv/bin/activate` (Linux/Mac)
4. Install dependencies: `pip install -r requirements.txt`
5. Launch Jupyter: `jupyter notebook`
6. Open `notebooks/rainfall_prediction.ipynb`

## Results
[To be updated after model training and evaluation]

## Author
Data Science Student - Final Course Project
