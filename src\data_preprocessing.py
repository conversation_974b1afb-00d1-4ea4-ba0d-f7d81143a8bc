"""
Data Preprocessing Module for Rainfall Prediction Project

This module contains functions for data cleaning, handling missing values,
outlier detection and treatment, and basic data preprocessing tasks.
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer, KNNImputer
import warnings
warnings.filterwarnings('ignore')


def load_data(file_path):
    """
    Load dataset from file path
    
    Args:
        file_path (str): Path to the dataset file
        
    Returns:
        pd.DataFrame: Loaded dataset
    """
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            raise ValueError("Unsupported file format. Please use CSV or Excel files.")
        
        print(f"Dataset loaded successfully. Shape: {df.shape}")
        return df
    except Exception as e:
        print(f"Error loading dataset: {str(e)}")
        return None


def basic_info(df):
    """
    Display basic information about the dataset
    
    Args:
        df (pd.DataFrame): Input dataframe
    """
    print("=== DATASET BASIC INFORMATION ===")
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print("\n=== DATA TYPES ===")
    print(df.dtypes)
    print("\n=== MISSING VALUES ===")
    missing_values = df.isnull().sum()
    missing_percent = (missing_values / len(df)) * 100
    missing_df = pd.DataFrame({
        'Missing Count': missing_values,
        'Missing Percentage': missing_percent
    })
    print(missing_df[missing_df['Missing Count'] > 0])


def detect_outliers(df, columns, method='iqr'):
    """
    Detect outliers in specified columns
    
    Args:
        df (pd.DataFrame): Input dataframe
        columns (list): List of column names to check for outliers
        method (str): Method to use ('iqr' or 'zscore')
        
    Returns:
        dict: Dictionary with outlier information for each column
    """
    outliers_info = {}
    
    for col in columns:
        if df[col].dtype in ['int64', 'float64']:
            if method == 'iqr':
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                
            elif method == 'zscore':
                z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                outliers = df[z_scores > 3]
            
            outliers_info[col] = {
                'count': len(outliers),
                'percentage': (len(outliers) / len(df)) * 100,
                'outlier_indices': outliers.index.tolist()
            }
    
    return outliers_info


def handle_missing_values(df, strategy='auto'):
    """
    Handle missing values in the dataset
    
    Args:
        df (pd.DataFrame): Input dataframe
        strategy (str): Strategy to handle missing values
                       ('auto', 'drop', 'mean', 'median', 'mode', 'knn')
        
    Returns:
        pd.DataFrame: Dataframe with handled missing values
    """
    df_processed = df.copy()
    
    if strategy == 'auto':
        # Automatic strategy based on data type and missing percentage
        for col in df_processed.columns:
            missing_pct = (df_processed[col].isnull().sum() / len(df_processed)) * 100
            
            if missing_pct == 0:
                continue
            elif missing_pct > 50:
                print(f"Dropping column {col} due to high missing percentage: {missing_pct:.2f}%")
                df_processed.drop(col, axis=1, inplace=True)
            elif df_processed[col].dtype == 'object':
                # Categorical variables - use mode
                mode_value = df_processed[col].mode()[0] if not df_processed[col].mode().empty else 'Unknown'
                df_processed[col].fillna(mode_value, inplace=True)
            else:
                # Numerical variables - use median
                median_value = df_processed[col].median()
                df_processed[col].fillna(median_value, inplace=True)
    
    elif strategy == 'drop':
        df_processed.dropna(inplace=True)
    
    elif strategy in ['mean', 'median']:
        imputer = SimpleImputer(strategy=strategy)
        numerical_cols = df_processed.select_dtypes(include=[np.number]).columns
        df_processed[numerical_cols] = imputer.fit_transform(df_processed[numerical_cols])
    
    elif strategy == 'knn':
        imputer = KNNImputer(n_neighbors=5)
        numerical_cols = df_processed.select_dtypes(include=[np.number]).columns
        df_processed[numerical_cols] = imputer.fit_transform(df_processed[numerical_cols])
    
    print(f"Missing values handled using '{strategy}' strategy")
    print(f"Remaining missing values: {df_processed.isnull().sum().sum()}")
    
    return df_processed


def encode_categorical_variables(df, target_column=None):
    """
    Encode categorical variables
    
    Args:
        df (pd.DataFrame): Input dataframe
        target_column (str): Name of target column (if any)
        
    Returns:
        tuple: (processed_dataframe, encoders_dict)
    """
    df_processed = df.copy()
    encoders = {}
    
    categorical_cols = df_processed.select_dtypes(include=['object']).columns
    if target_column and target_column in categorical_cols:
        categorical_cols = categorical_cols.drop(target_column)
    
    for col in categorical_cols:
        if df_processed[col].nunique() <= 10:  # Use Label Encoding for low cardinality
            le = LabelEncoder()
            df_processed[col] = le.fit_transform(df_processed[col].astype(str))
            encoders[col] = le
        else:  # Use One-Hot Encoding for high cardinality
            df_encoded = pd.get_dummies(df_processed[col], prefix=col)
            df_processed = pd.concat([df_processed, df_encoded], axis=1)
            df_processed.drop(col, axis=1, inplace=True)
            encoders[col] = 'one_hot'
    
    # Handle target column separately if it's categorical
    if target_column and target_column in df.columns and df[target_column].dtype == 'object':
        le_target = LabelEncoder()
        df_processed[target_column] = le_target.fit_transform(df_processed[target_column])
        encoders[target_column] = le_target
    
    return df_processed, encoders


def scale_features(X_train, X_test=None, method='standard'):
    """
    Scale numerical features
    
    Args:
        X_train (pd.DataFrame): Training features
        X_test (pd.DataFrame): Testing features (optional)
        method (str): Scaling method ('standard', 'minmax', 'robust')
        
    Returns:
        tuple: (scaled_X_train, scaled_X_test, scaler)
    """
    if method == 'standard':
        scaler = StandardScaler()
    else:
        raise ValueError("Currently only 'standard' scaling is implemented")
    
    X_train_scaled = scaler.fit_transform(X_train)
    X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns, index=X_train.index)
    
    if X_test is not None:
        X_test_scaled = scaler.transform(X_test)
        X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns, index=X_test.index)
        return X_train_scaled, X_test_scaled, scaler
    
    return X_train_scaled, None, scaler
