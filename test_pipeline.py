#!/usr/bin/env python3
"""
Test script for the Rainfall Prediction ML Pipeline
This script tests all the major components of the pipeline
"""

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Import our custom modules
from data_preprocessing import load_data, basic_info, handle_missing_values, encode_categorical_variables
from feature_engineering import create_weather_features, prepare_final_features
from model_training import split_data, train_logistic_regression, train_random_forest, train_xgboost
from model_training import evaluate_model, compare_models
from visualization import plot_target_distribution

def test_pipeline():
    """Test the complete ML pipeline"""
    
    print("🚀 Testing Rainfall Prediction ML Pipeline")
    print("=" * 50)
    
    # 1. Load data
    print("\n1. Loading data...")
    df = load_data('data/weather_data.csv')
    if df is None:
        print("❌ Failed to load data")
        return False
    print(f"✅ Data loaded successfully: {df.shape}")
    
    # 2. Basic info
    print("\n2. Dataset basic information...")
    basic_info(df)
    
    # 3. Handle missing values
    print("\n3. Handling missing values...")
    df_cleaned = handle_missing_values(df, strategy='auto')
    print(f"✅ Missing values handled: {df_cleaned.shape}")
    
    # 4. Encode categorical variables
    print("\n4. Encoding categorical variables...")
    df_encoded, encoders = encode_categorical_variables(df_cleaned, target_column='RainTomorrow')
    print(f"✅ Categorical variables encoded: {df_encoded.shape}")
    
    # 5. Feature engineering
    print("\n5. Feature engineering...")
    df_features = create_weather_features(df_encoded)
    print(f"✅ Features engineered: {df_features.shape}")
    
    # 6. Prepare final features
    print("\n6. Preparing final features...")
    X, y, selected_features = prepare_final_features(
        df_features, 
        target_column='RainTomorrow', 
        feature_selection_method='auto',
        n_features=15
    )
    print(f"✅ Final features prepared: X{X.shape}, y{y.shape}")
    print(f"Selected features: {len(selected_features)}")
    
    # 7. Split data
    print("\n7. Splitting data...")
    X_train, X_test, y_train, y_test = split_data(X, y, test_size=0.2, random_state=42)
    print(f"✅ Data split: Train{X_train.shape}, Test{X_test.shape}")
    
    # 8. Train models
    print("\n8. Training models...")
    
    # Train Logistic Regression
    print("   Training Logistic Regression...")
    lr_model = train_logistic_regression(X_train, y_train)
    
    # Train Random Forest
    print("   Training Random Forest...")
    rf_model = train_random_forest(X_train, y_train)
    
    # Train XGBoost
    print("   Training XGBoost...")
    xgb_model = train_xgboost(X_train, y_train)
    
    print("✅ All models trained successfully")
    
    # 9. Evaluate models
    print("\n9. Evaluating models...")
    models = {
        'Logistic Regression': lr_model,
        'Random Forest': rf_model,
        'XGBoost': xgb_model
    }
    
    results_df = compare_models(models, X_test, y_test)
    print("✅ Model evaluation completed")
    
    # 10. Display results
    print("\n10. Results Summary:")
    print("=" * 30)
    print(results_df.round(4))
    
    best_model = results_df.iloc[0]['model_name']
    best_accuracy = results_df.iloc[0]['accuracy']
    
    print(f"\n🏆 Best Model: {best_model}")
    print(f"🎯 Best Accuracy: {best_accuracy:.4f} ({best_accuracy*100:.1f}%)")
    
    print("\n✅ Pipeline test completed successfully!")
    return True

def test_visualization():
    """Test visualization functions"""
    print("\n📊 Testing visualization functions...")
    
    try:
        # Load data for visualization test
        df = pd.read_csv('data/weather_data.csv')
        
        # Test target distribution plot
        print("   Testing target distribution plot...")
        plot_target_distribution(df['RainTomorrow'], "Test Target Distribution")
        print("✅ Visualization test completed")
        
    except Exception as e:
        print(f"⚠️ Visualization test failed: {e}")
        print("Note: This is expected in non-interactive environments")

if __name__ == "__main__":
    print("🧪 RAINFALL PREDICTION ML PIPELINE TEST")
    print("=" * 60)
    
    # Test main pipeline
    success = test_pipeline()
    
    # Test visualization (may fail in non-interactive environment)
    test_visualization()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The Rainfall Prediction ML Pipeline is ready to use.")
    else:
        print("\n❌ TESTS FAILED!")
        print("Please check the error messages above.")
    
    print("\n" + "=" * 60)
