"""
Visualization Module for Rainfall Prediction Project

This module contains functions for creating various plots and visualizations
for data exploration, model evaluation, and results presentation.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, roc_curve, auc
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('default')
sns.set_palette("husl")


def plot_data_overview(df, figsize=(15, 10)):
    """
    Create overview plots of the dataset
    
    Args:
        df (pd.DataFrame): Input dataframe
        figsize (tuple): Figure size
    """
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    
    # Dataset shape info
    axes[0, 0].text(0.1, 0.8, f"Dataset Shape: {df.shape}", fontsize=14, fontweight='bold')
    axes[0, 0].text(0.1, 0.6, f"Number of Features: {df.shape[1]}", fontsize=12)
    axes[0, 0].text(0.1, 0.4, f"Number of Samples: {df.shape[0]}", fontsize=12)
    axes[0, 0].text(0.1, 0.2, f"Memory Usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB", fontsize=12)
    axes[0, 0].set_xlim(0, 1)
    axes[0, 0].set_ylim(0, 1)
    axes[0, 0].axis('off')
    axes[0, 0].set_title('Dataset Information')
    
    # Missing values
    missing_data = df.isnull().sum()
    missing_data = missing_data[missing_data > 0].sort_values(ascending=False)
    if len(missing_data) > 0:
        missing_data.head(10).plot(kind='bar', ax=axes[0, 1])
        axes[0, 1].set_title('Missing Values by Column')
        axes[0, 1].set_ylabel('Count')
        plt.setp(axes[0, 1].xaxis.get_majorticklabels(), rotation=45)
    else:
        axes[0, 1].text(0.5, 0.5, 'No Missing Values', ha='center', va='center', fontsize=14)
        axes[0, 1].set_xlim(0, 1)
        axes[0, 1].set_ylim(0, 1)
        axes[0, 1].axis('off')
    
    # Data types
    dtype_counts = df.dtypes.value_counts()
    axes[1, 0].pie(dtype_counts.values, labels=dtype_counts.index, autopct='%1.1f%%')
    axes[1, 0].set_title('Data Types Distribution')
    
    # Numerical columns distribution
    numerical_cols = df.select_dtypes(include=[np.number]).columns
    if len(numerical_cols) > 0:
        axes[1, 1].hist([len(df[col].dropna()) for col in numerical_cols], bins=10, alpha=0.7)
        axes[1, 1].set_title('Distribution of Non-null Values\nin Numerical Columns')
        axes[1, 1].set_xlabel('Number of Non-null Values')
        axes[1, 1].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.show()


def plot_target_distribution(y, title="Target Variable Distribution"):
    """
    Plot target variable distribution
    
    Args:
        y (pd.Series): Target variable
        title (str): Plot title
    """
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Count plot
    y.value_counts().plot(kind='bar', ax=axes[0])
    axes[0].set_title(f'{title} - Counts')
    axes[0].set_ylabel('Count')
    axes[0].tick_params(axis='x', rotation=45)
    
    # Percentage plot
    y.value_counts(normalize=True).plot(kind='bar', ax=axes[1])
    axes[1].set_title(f'{title} - Proportions')
    axes[1].set_ylabel('Proportion')
    axes[1].tick_params(axis='x', rotation=45)
    
    # Add value labels on bars
    for ax in axes:
        for i, v in enumerate(ax.patches):
            ax.text(v.get_x() + v.get_width()/2, v.get_height() + 0.01, 
                   f'{v.get_height():.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()


def plot_correlation_matrix(df, figsize=(12, 10), method='pearson'):
    """
    Plot correlation matrix heatmap
    
    Args:
        df (pd.DataFrame): Input dataframe
        figsize (tuple): Figure size
        method (str): Correlation method
    """
    # Select only numerical columns
    numerical_df = df.select_dtypes(include=[np.number])
    
    if numerical_df.empty:
        print("No numerical columns found for correlation analysis")
        return
    
    # Calculate correlation matrix
    corr_matrix = numerical_df.corr(method=method)
    
    # Create heatmap
    plt.figure(figsize=figsize)
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": 0.8})
    plt.title(f'Correlation Matrix ({method.capitalize()})')
    plt.tight_layout()
    plt.show()


def plot_feature_distributions(df, columns=None, figsize=(15, 10)):
    """
    Plot distributions of selected features
    
    Args:
        df (pd.DataFrame): Input dataframe
        columns (list): List of columns to plot (if None, plot all numerical)
        figsize (tuple): Figure size
    """
    if columns is None:
        columns = df.select_dtypes(include=[np.number]).columns.tolist()
    
    n_cols = min(4, len(columns))
    n_rows = (len(columns) + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize)
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    
    for i, col in enumerate(columns):
        row = i // n_cols
        col_idx = i % n_cols
        
        if len(columns) == 1:
            ax = axes[0]
        else:
            ax = axes[row, col_idx]
        
        df[col].hist(bins=30, alpha=0.7, ax=ax)
        ax.set_title(f'Distribution of {col}')
        ax.set_xlabel(col)
        ax.set_ylabel('Frequency')
    
    # Hide empty subplots
    for i in range(len(columns), n_rows * n_cols):
        row = i // n_cols
        col_idx = i % n_cols
        axes[row, col_idx].axis('off')
    
    plt.tight_layout()
    plt.show()


def plot_confusion_matrix(y_true, y_pred, labels=None, title="Confusion Matrix"):
    """
    Plot confusion matrix
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        labels: Label names
        title (str): Plot title
    """
    cm = confusion_matrix(y_true, y_pred)
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=labels, yticklabels=labels)
    plt.title(title)
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.tight_layout()
    plt.show()
    
    # Print classification metrics
    from sklearn.metrics import classification_report
    print("\nClassification Report:")
    print(classification_report(y_true, y_pred, target_names=labels))


def plot_roc_curve(y_true, y_pred_proba, title="ROC Curve"):
    """
    Plot ROC curve
    
    Args:
        y_true: True labels
        y_pred_proba: Predicted probabilities
        title (str): Plot title
    """
    fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
    roc_auc = auc(fpr, tpr)
    
    plt.figure(figsize=(8, 6))
    plt.plot(fpr, tpr, color='darkorange', lw=2, 
             label=f'ROC curve (AUC = {roc_auc:.3f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', 
             label='Random Classifier')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title(title)
    plt.legend(loc="lower right")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def plot_feature_importance(feature_importance_df, top_n=20, title="Feature Importance"):
    """
    Plot feature importance
    
    Args:
        feature_importance_df (pd.DataFrame): DataFrame with 'Feature' and 'Importance' columns
        top_n (int): Number of top features to display
        title (str): Plot title
    """
    top_features = feature_importance_df.head(top_n)
    
    plt.figure(figsize=(10, 8))
    sns.barplot(data=top_features, y='Feature', x='Importance', palette='viridis')
    plt.title(f'{title} (Top {top_n})')
    plt.xlabel('Importance Score')
    plt.tight_layout()
    plt.show()


def plot_model_comparison(results_df, metric='accuracy'):
    """
    Plot model comparison
    
    Args:
        results_df (pd.DataFrame): DataFrame with model results
        metric (str): Metric to compare
    """
    plt.figure(figsize=(10, 6))
    sns.barplot(data=results_df, x='model_name', y=metric, palette='Set2')
    plt.title(f'Model Comparison - {metric.upper()}')
    plt.xlabel('Model')
    plt.ylabel(metric.upper())
    plt.xticks(rotation=45)
    
    # Add value labels on bars
    for i, v in enumerate(plt.gca().patches):
        plt.text(v.get_x() + v.get_width()/2, v.get_height() + 0.001, 
                f'{v.get_height():.4f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()


def plot_learning_curves(model, X, y, cv=5, train_sizes=np.linspace(0.1, 1.0, 10)):
    """
    Plot learning curves for a model
    
    Args:
        model: Machine learning model
        X: Features
        y: Target
        cv (int): Cross-validation folds
        train_sizes: Training set sizes to evaluate
    """
    from sklearn.model_selection import learning_curve
    
    train_sizes, train_scores, val_scores = learning_curve(
        model, X, y, cv=cv, train_sizes=train_sizes, 
        scoring='accuracy', n_jobs=-1
    )
    
    train_mean = np.mean(train_scores, axis=1)
    train_std = np.std(train_scores, axis=1)
    val_mean = np.mean(val_scores, axis=1)
    val_std = np.std(val_scores, axis=1)
    
    plt.figure(figsize=(10, 6))
    plt.plot(train_sizes, train_mean, 'o-', color='blue', label='Training Score')
    plt.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, 
                     alpha=0.1, color='blue')
    
    plt.plot(train_sizes, val_mean, 'o-', color='red', label='Validation Score')
    plt.fill_between(train_sizes, val_mean - val_std, val_mean + val_std, 
                     alpha=0.1, color='red')
    
    plt.xlabel('Training Set Size')
    plt.ylabel('Accuracy Score')
    plt.title('Learning Curves')
    plt.legend(loc='best')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def save_plot(filename, dpi=300, bbox_inches='tight'):
    """
    Save the current plot
    
    Args:
        filename (str): Filename to save
        dpi (int): Resolution
        bbox_inches (str): Bounding box setting
    """
    plt.savefig(filename, dpi=dpi, bbox_inches=bbox_inches)
    print(f"Plot saved as {filename}")
