#!/usr/bin/env python3
"""
Simple Flask API for Rainfall Prediction
This demonstrates how to deploy the model as a web service

To run:
1. pip install flask
2. python api_example.py
3. Test with: curl -X POST http://localhost:5000/predict -H "Content-Type: application/json" -d @sample_request.json
"""

try:
    from flask import Flask, request, jsonify
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask not installed. Install with: pip install flask")

import sys
import os
sys.path.append('src')

import pandas as pd
import numpy as np
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import our deployment class
from deploy_model import RainfallPredictor

# Initialize Flask app
if FLASK_AVAILABLE:
    app = Flask(__name__)
    
    # Global predictor instance
    predictor = None
    
    def initialize_model():
        """Initialize the model on startup"""
        global predictor
        predictor = RainfallPredictor()
        success = predictor.load_model()
        if success:
            print("✅ Model loaded successfully for API")
        else:
            print("❌ Failed to load model for API")
        return success
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'model_loaded': predictor is not None and predictor.is_loaded
        })
    
    @app.route('/predict', methods=['POST'])
    def predict_rainfall():
        """Main prediction endpoint"""
        try:
            # Check if model is loaded
            if not predictor or not predictor.is_loaded:
                return jsonify({
                    'error': 'Model not loaded',
                    'message': 'Please ensure the model is properly trained and saved'
                }), 500
            
            # Get JSON data from request
            data = request.get_json()
            
            if not data:
                return jsonify({
                    'error': 'No data provided',
                    'message': 'Please provide weather data in JSON format'
                }), 400
            
            # Validate required fields
            required_fields = [
                'MinTemp', 'MaxTemp', 'Humidity9am', 'Humidity3pm',
                'Pressure9am', 'Pressure3pm', 'WindSpeed9am', 'WindSpeed3pm'
            ]
            
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return jsonify({
                    'error': 'Missing required fields',
                    'missing_fields': missing_fields,
                    'required_fields': required_fields
                }), 400
            
            # Make prediction
            result = predictor.predict_single(data)
            
            # Return result
            response = {
                'prediction': result['prediction'],
                'confidence': result['confidence'],
                'rain_probability': result['rain_probability'],
                'timestamp': datetime.now().isoformat(),
                'input_data': data
            }
            
            return jsonify(response)
            
        except Exception as e:
            return jsonify({
                'error': 'Prediction failed',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }), 500
    
    @app.route('/predict/batch', methods=['POST'])
    def predict_batch():
        """Batch prediction endpoint"""
        try:
            if not predictor or not predictor.is_loaded:
                return jsonify({'error': 'Model not loaded'}), 500
            
            data = request.get_json()
            
            if not data or 'samples' not in data:
                return jsonify({
                    'error': 'Invalid data format',
                    'message': 'Please provide data in format: {"samples": [...]}'
                }), 400
            
            samples = data['samples']
            if not isinstance(samples, list):
                return jsonify({
                    'error': 'Samples must be a list'
                }), 400
            
            # Convert to DataFrame
            df = pd.DataFrame(samples)
            
            # Make predictions
            predictions, probabilities = predictor.predict(df)
            
            # Format results
            results = []
            for i, (pred, prob) in enumerate(zip(predictions, probabilities)):
                results.append({
                    'sample_id': i,
                    'prediction': 'Rain' if pred == 1 else 'No Rain',
                    'rain_probability': float(prob[1]) if prob is not None else None,
                    'confidence': float(np.max(prob)) if prob is not None else None
                })
            
            return jsonify({
                'predictions': results,
                'total_samples': len(results),
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            return jsonify({
                'error': 'Batch prediction failed',
                'message': str(e)
            }), 500
    
    @app.route('/model/info', methods=['GET'])
    def model_info():
        """Get model information"""
        if not predictor or not predictor.is_loaded:
            return jsonify({'error': 'Model not loaded'}), 500
        
        info = {
            'model_type': type(predictor.model).__name__,
            'features_count': len(predictor.selected_features) if predictor.selected_features else 0,
            'selected_features': predictor.selected_features,
            'preprocessing_components': {
                'scaler': predictor.scaler is not None,
                'encoders': predictor.encoders is not None
            },
            'timestamp': datetime.now().isoformat()
        }
        
        return jsonify(info)

def create_sample_request_file():
    """Create a sample JSON request file for testing"""
    sample_request = {
        "Date": "2024-01-15",
        "Location": "Sydney",
        "MinTemp": 18.5,
        "MaxTemp": 28.3,
        "Rainfall": 0.0,
        "Evaporation": 4.2,
        "Sunshine": 9.1,
        "WindGustDir": "NW",
        "WindGustSpeed": 35.0,
        "WindDir9am": "N",
        "WindDir3pm": "NW",
        "WindSpeed9am": 12.0,
        "WindSpeed3pm": 18.0,
        "Humidity9am": 65.0,
        "Humidity3pm": 45.0,
        "Pressure9am": 1018.2,
        "Pressure3pm": 1015.8,
        "Cloud9am": 3,
        "Cloud3pm": 5,
        "Temp9am": 20.1,
        "Temp3pm": 26.7,
        "RainToday": "No"
    }
    
    with open('sample_request.json', 'w') as f:
        json.dump(sample_request, f, indent=2)
    
    print("✅ Created sample_request.json for API testing")

def create_batch_request_file():
    """Create a sample batch request file"""
    batch_request = {
        "samples": [
            {
                "MinTemp": 18.5, "MaxTemp": 28.3, "Humidity9am": 65.0, "Humidity3pm": 45.0,
                "Pressure9am": 1018.2, "Pressure3pm": 1015.8, "WindSpeed9am": 12.0, "WindSpeed3pm": 18.0,
                "Rainfall": 0.0, "RainToday": "No"
            },
            {
                "MinTemp": 15.2, "MaxTemp": 22.1, "Humidity9am": 85.0, "Humidity3pm": 70.0,
                "Pressure9am": 1008.5, "Pressure3pm": 1005.2, "WindSpeed9am": 25.0, "WindSpeed3pm": 30.0,
                "Rainfall": 2.5, "RainToday": "Yes"
            }
        ]
    }
    
    with open('sample_batch_request.json', 'w') as f:
        json.dump(batch_request, f, indent=2)
    
    print("✅ Created sample_batch_request.json for batch API testing")

if __name__ == '__main__':
    if not FLASK_AVAILABLE:
        print("❌ Flask is required to run the API")
        print("Install with: pip install flask")
        sys.exit(1)
    
    print("🚀 Starting Rainfall Prediction API")
    print("=" * 40)
    
    # Create sample request files
    create_sample_request_file()
    create_batch_request_file()
    
    # Initialize model
    if initialize_model():
        print("\n🌐 API Endpoints:")
        print("- GET  /health          - Health check")
        print("- POST /predict         - Single prediction")
        print("- POST /predict/batch   - Batch predictions")
        print("- GET  /model/info      - Model information")
        
        print("\n📝 Testing Commands:")
        print("curl -X GET http://localhost:5000/health")
        print("curl -X POST http://localhost:5000/predict -H 'Content-Type: application/json' -d @sample_request.json")
        print("curl -X POST http://localhost:5000/predict/batch -H 'Content-Type: application/json' -d @sample_batch_request.json")
        
        print("\n🚀 Starting server on http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to initialize model. Please train the model first.")
        print("Run: python test_pipeline.py")
