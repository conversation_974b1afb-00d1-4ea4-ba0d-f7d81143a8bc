"""
Feature Engineering Module for Rainfall Prediction Project

This module contains functions for creating new features, feature selection,
and advanced feature engineering techniques.
"""

import pandas as pd
import numpy as np
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.feature_selection import RFE
from sklearn.ensemble import RandomForestClassifier
import warnings
warnings.filterwarnings('ignore')


def create_weather_features(df):
    """
    Create new weather-related features from existing variables
    
    Args:
        df (pd.DataFrame): Input dataframe with weather data
        
    Returns:
        pd.DataFrame: Dataframe with new engineered features
    """
    df_features = df.copy()
    
    # Temperature-related features
    if 'MinTemp' in df.columns and 'MaxTemp' in df.columns:
        df_features['TempRange'] = df_features['MaxTemp'] - df_features['MinTemp']
        df_features['AvgTemp'] = (df_features['MaxTemp'] + df_features['MinTemp']) / 2
    
    # Humidity-related features
    if 'Humidity9am' in df.columns and 'Humidity3pm' in df.columns:
        df_features['HumidityRange'] = df_features['Humidity9am'] - df_features['Humidity3pm']
        df_features['AvgHumidity'] = (df_features['Humidity9am'] + df_features['Humidity3pm']) / 2
    
    # Pressure-related features
    if 'Pressure9am' in df.columns and 'Pressure3pm' in df.columns:
        df_features['PressureChange'] = df_features['Pressure3pm'] - df_features['Pressure9am']
        df_features['AvgPressure'] = (df_features['Pressure9am'] + df_features['Pressure3pm']) / 2
    
    # Wind-related features
    if 'WindSpeed9am' in df.columns and 'WindSpeed3pm' in df.columns:
        df_features['WindSpeedChange'] = df_features['WindSpeed3pm'] - df_features['WindSpeed9am']
        df_features['MaxWindSpeed'] = df_features[['WindSpeed9am', 'WindSpeed3pm']].max(axis=1)
    
    # Temperature and humidity interaction
    if 'AvgTemp' in df_features.columns and 'AvgHumidity' in df_features.columns:
        df_features['TempHumidityInteraction'] = df_features['AvgTemp'] * df_features['AvgHumidity']
    
    # Seasonal features (if date column exists)
    if 'Date' in df.columns:
        df_features['Date'] = pd.to_datetime(df_features['Date'])
        df_features['Month'] = df_features['Date'].dt.month
        # Create numerical season encoding instead of categorical
        df_features['Season'] = df_features['Month'].map({
            12: 0, 1: 0, 2: 0,  # Summer = 0
            3: 1, 4: 1, 5: 1,   # Autumn = 1
            6: 2, 7: 2, 8: 2,   # Winter = 2
            9: 3, 10: 3, 11: 3  # Spring = 3
        })
        df_features['DayOfYear'] = df_features['Date'].dt.dayofyear
        df_features['IsWeekend'] = df_features['Date'].dt.dayofweek.isin([5, 6]).astype(int)
        # Drop the original Date column as it's not needed for modeling
        df_features = df_features.drop('Date', axis=1)
    
    # Rainfall-related features
    if 'Rainfall' in df.columns:
        # Create numerical rainfall categories instead of categorical
        df_features['RainfallCategory'] = pd.cut(df_features['Rainfall'],
                                                bins=[-np.inf, 0, 1, 5, 25, np.inf],
                                                labels=[0, 1, 2, 3, 4])
        df_features['HighRainfall'] = (df_features['Rainfall'] > df_features['Rainfall'].quantile(0.75)).astype(int)
    
    print(f"Feature engineering completed. New shape: {df_features.shape}")
    return df_features


def create_lag_features(df, target_col, lag_periods=[1, 2, 3]):
    """
    Create lag features for time series data
    
    Args:
        df (pd.DataFrame): Input dataframe
        target_col (str): Target column name
        lag_periods (list): List of lag periods to create
        
    Returns:
        pd.DataFrame: Dataframe with lag features
    """
    df_lag = df.copy()
    
    for lag in lag_periods:
        df_lag[f'{target_col}_lag_{lag}'] = df_lag[target_col].shift(lag)
    
    # Remove rows with NaN values created by lagging
    df_lag.dropna(inplace=True)
    
    return df_lag


def select_features_univariate(X, y, k=20, score_func=f_classif):
    """
    Select features using univariate statistical tests
    
    Args:
        X (pd.DataFrame): Feature matrix
        y (pd.Series): Target variable
        k (int): Number of features to select
        score_func: Scoring function to use
        
    Returns:
        tuple: (selected_features, selector)
    """
    selector = SelectKBest(score_func=score_func, k=k)
    X_selected = selector.fit_transform(X, y)
    
    selected_features = X.columns[selector.get_support()].tolist()
    feature_scores = pd.DataFrame({
        'Feature': X.columns,
        'Score': selector.scores_,
        'Selected': selector.get_support()
    }).sort_values('Score', ascending=False)
    
    print(f"Selected {len(selected_features)} features using univariate selection")
    print("Top 10 features by score:")
    print(feature_scores.head(10))
    
    return selected_features, selector


def select_features_rfe(X, y, n_features=20, estimator=None):
    """
    Select features using Recursive Feature Elimination
    
    Args:
        X (pd.DataFrame): Feature matrix
        y (pd.Series): Target variable
        n_features (int): Number of features to select
        estimator: Estimator to use for RFE
        
    Returns:
        tuple: (selected_features, rfe_selector)
    """
    if estimator is None:
        estimator = RandomForestClassifier(n_estimators=100, random_state=42)
    
    rfe = RFE(estimator=estimator, n_features_to_select=n_features)
    rfe.fit(X, y)
    
    selected_features = X.columns[rfe.support_].tolist()
    feature_ranking = pd.DataFrame({
        'Feature': X.columns,
        'Ranking': rfe.ranking_,
        'Selected': rfe.support_
    }).sort_values('Ranking')
    
    print(f"Selected {len(selected_features)} features using RFE")
    print("Feature rankings:")
    print(feature_ranking.head(10))
    
    return selected_features, rfe


def get_feature_importance(X, y, method='random_forest'):
    """
    Get feature importance using different methods
    
    Args:
        X (pd.DataFrame): Feature matrix
        y (pd.Series): Target variable
        method (str): Method to use ('random_forest', 'mutual_info')
        
    Returns:
        pd.DataFrame: Feature importance scores
    """
    if method == 'random_forest':
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)
        importance_scores = rf.feature_importances_
        
    elif method == 'mutual_info':
        importance_scores = mutual_info_classif(X, y, random_state=42)
    
    else:
        raise ValueError("Method must be 'random_forest' or 'mutual_info'")
    
    feature_importance = pd.DataFrame({
        'Feature': X.columns,
        'Importance': importance_scores
    }).sort_values('Importance', ascending=False)
    
    return feature_importance


def create_interaction_features(df, feature_pairs):
    """
    Create interaction features between specified feature pairs
    
    Args:
        df (pd.DataFrame): Input dataframe
        feature_pairs (list): List of tuples containing feature pairs
        
    Returns:
        pd.DataFrame: Dataframe with interaction features
    """
    df_interactions = df.copy()
    
    for feat1, feat2 in feature_pairs:
        if feat1 in df.columns and feat2 in df.columns:
            # Multiplicative interaction
            df_interactions[f'{feat1}_{feat2}_mult'] = df_interactions[feat1] * df_interactions[feat2]
            
            # Additive interaction
            df_interactions[f'{feat1}_{feat2}_add'] = df_interactions[feat1] + df_interactions[feat2]
            
            # Ratio interaction (avoid division by zero)
            df_interactions[f'{feat1}_{feat2}_ratio'] = np.where(
                df_interactions[feat2] != 0,
                df_interactions[feat1] / df_interactions[feat2],
                0
            )
    
    return df_interactions


def remove_correlated_features(df, threshold=0.95):
    """
    Remove highly correlated features

    Args:
        df (pd.DataFrame): Input dataframe
        threshold (float): Correlation threshold

    Returns:
        pd.DataFrame: Dataframe with correlated features removed
    """
    # Select only numerical columns for correlation analysis
    numerical_cols = df.select_dtypes(include=[np.number]).columns
    df_numerical = df[numerical_cols]

    if df_numerical.empty:
        print("No numerical columns found for correlation analysis")
        return df

    # Calculate correlation matrix
    corr_matrix = df_numerical.corr().abs()

    # Find pairs of highly correlated features
    upper_triangle = corr_matrix.where(
        np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
    )

    # Find features to drop
    to_drop = [column for column in upper_triangle.columns if any(upper_triangle[column] > threshold)]

    print(f"Removing {len(to_drop)} highly correlated features (threshold: {threshold})")
    if to_drop:
        print(f"Features removed: {to_drop}")

    return df.drop(columns=to_drop)


def prepare_final_features(df, target_column, feature_selection_method='auto', n_features=None):
    """
    Prepare final feature set for modeling
    
    Args:
        df (pd.DataFrame): Input dataframe
        target_column (str): Target column name
        feature_selection_method (str): Method for feature selection
        n_features (int): Number of features to select
        
    Returns:
        tuple: (X, y, selected_features)
    """
    # Separate features and target
    X = df.drop(columns=[target_column])
    y = df[target_column]
    
    # Remove highly correlated features
    X = remove_correlated_features(X, threshold=0.95)
    
    # Feature selection
    if feature_selection_method == 'auto':
        if n_features is None:
            n_features = min(20, X.shape[1])
        
        # Use Random Forest feature importance
        feature_importance = get_feature_importance(X, y, method='random_forest')
        selected_features = feature_importance.head(n_features)['Feature'].tolist()
        X_final = X[selected_features]
        
    elif feature_selection_method == 'univariate':
        if n_features is None:
            n_features = min(20, X.shape[1])
        selected_features, _ = select_features_univariate(X, y, k=n_features)
        X_final = X[selected_features]
        
    elif feature_selection_method == 'rfe':
        if n_features is None:
            n_features = min(20, X.shape[1])
        selected_features, _ = select_features_rfe(X, y, n_features=n_features)
        X_final = X[selected_features]
        
    else:
        X_final = X
        selected_features = X.columns.tolist()
    
    print(f"Final feature set prepared with {X_final.shape[1]} features")
    return X_final, y, selected_features
