"""
Model Training Module for Rainfall Prediction Project

This module contains functions for training different classification models,
hyperparameter tuning, and model evaluation.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, RandomizedSearchCV
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.metrics import classification_report, confusion_matrix, roc_curve
import xgboost as xgb
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import RandomUnderSampler
import joblib
import warnings
warnings.filterwarnings('ignore')


def split_data(X, y, test_size=0.2, random_state=42, stratify=True):
    """
    Split data into training and testing sets
    
    Args:
        X (pd.DataFrame): Feature matrix
        y (pd.Series): Target variable
        test_size (float): Proportion of test set
        random_state (int): Random state for reproducibility
        stratify (bool): Whether to stratify the split
        
    Returns:
        tuple: (X_train, X_test, y_train, y_test)
    """
    stratify_param = y if stratify else None
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=stratify_param
    )
    
    print(f"Data split completed:")
    print(f"Training set: {X_train.shape[0]} samples")
    print(f"Testing set: {X_test.shape[0]} samples")
    print(f"Training target distribution:\n{y_train.value_counts(normalize=True)}")
    
    return X_train, X_test, y_train, y_test


def handle_class_imbalance(X_train, y_train, method='smote'):
    """
    Handle class imbalance in the training data
    
    Args:
        X_train (pd.DataFrame): Training features
        y_train (pd.Series): Training target
        method (str): Method to handle imbalance ('smote', 'undersample', 'none')
        
    Returns:
        tuple: (X_resampled, y_resampled)
    """
    print(f"Original class distribution:\n{y_train.value_counts()}")
    
    if method == 'smote':
        smote = SMOTE(random_state=42)
        X_resampled, y_resampled = smote.fit_resample(X_train, y_train)
        
    elif method == 'undersample':
        undersampler = RandomUnderSampler(random_state=42)
        X_resampled, y_resampled = undersampler.fit_resample(X_train, y_train)
        
    else:
        X_resampled, y_resampled = X_train, y_train
    
    if method != 'none':
        print(f"After {method} resampling:\n{pd.Series(y_resampled).value_counts()}")
    
    return X_resampled, y_resampled


def train_logistic_regression(X_train, y_train, random_state=42):
    """
    Train Logistic Regression model
    
    Args:
        X_train (pd.DataFrame): Training features
        y_train (pd.Series): Training target
        random_state (int): Random state
        
    Returns:
        sklearn.linear_model.LogisticRegression: Trained model
    """
    model = LogisticRegression(random_state=random_state, max_iter=1000)
    model.fit(X_train, y_train)
    
    print("Logistic Regression model trained successfully")
    return model


def train_random_forest(X_train, y_train, random_state=42):
    """
    Train Random Forest model
    
    Args:
        X_train (pd.DataFrame): Training features
        y_train (pd.Series): Training target
        random_state (int): Random state
        
    Returns:
        sklearn.ensemble.RandomForestClassifier: Trained model
    """
    model = RandomForestClassifier(
        n_estimators=100,
        random_state=random_state,
        n_jobs=-1
    )
    model.fit(X_train, y_train)
    
    print("Random Forest model trained successfully")
    return model


def train_xgboost(X_train, y_train, random_state=42):
    """
    Train XGBoost model
    
    Args:
        X_train (pd.DataFrame): Training features
        y_train (pd.Series): Training target
        random_state (int): Random state
        
    Returns:
        xgboost.XGBClassifier: Trained model
    """
    model = xgb.XGBClassifier(
        random_state=random_state,
        eval_metric='logloss'
    )
    model.fit(X_train, y_train)
    
    print("XGBoost model trained successfully")
    return model


def evaluate_model(model, X_test, y_test, model_name="Model"):
    """
    Evaluate model performance
    
    Args:
        model: Trained model
        X_test (pd.DataFrame): Test features
        y_test (pd.Series): Test target
        model_name (str): Name of the model
        
    Returns:
        dict: Dictionary containing evaluation metrics
    """
    # Make predictions
    y_pred = model.predict(X_test)
    y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # Calculate metrics
    metrics = {
        'model_name': model_name,
        'accuracy': accuracy_score(y_test, y_pred),
        'precision': precision_score(y_test, y_pred, average='weighted'),
        'recall': recall_score(y_test, y_pred, average='weighted'),
        'f1_score': f1_score(y_test, y_pred, average='weighted'),
    }
    
    if y_pred_proba is not None:
        metrics['roc_auc'] = roc_auc_score(y_test, y_pred_proba)
    
    # Print results
    print(f"\n=== {model_name} Performance ===")
    for metric, value in metrics.items():
        if metric != 'model_name':
            print(f"{metric.upper()}: {value:.4f}")
    
    return metrics


def cross_validate_model(model, X, y, cv=5, scoring='accuracy'):
    """
    Perform cross-validation on a model
    
    Args:
        model: Model to evaluate
        X (pd.DataFrame): Features
        y (pd.Series): Target
        cv (int): Number of cross-validation folds
        scoring (str): Scoring metric
        
    Returns:
        dict: Cross-validation results
    """
    cv_scores = cross_val_score(model, X, y, cv=cv, scoring=scoring)
    
    results = {
        'mean_score': cv_scores.mean(),
        'std_score': cv_scores.std(),
        'scores': cv_scores
    }
    
    print(f"Cross-validation {scoring}: {results['mean_score']:.4f} (+/- {results['std_score']*2:.4f})")
    
    return results


def hyperparameter_tuning_rf(X_train, y_train, cv=3, n_iter=20):
    """
    Perform hyperparameter tuning for Random Forest
    
    Args:
        X_train (pd.DataFrame): Training features
        y_train (pd.Series): Training target
        cv (int): Cross-validation folds
        n_iter (int): Number of iterations for random search
        
    Returns:
        sklearn.ensemble.RandomForestClassifier: Best model
    """
    param_dist = {
        'n_estimators': [50, 100, 200, 300],
        'max_depth': [None, 10, 20, 30],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'max_features': ['sqrt', 'log2', None]
    }
    
    rf = RandomForestClassifier(random_state=42)
    
    random_search = RandomizedSearchCV(
        rf, param_dist, n_iter=n_iter, cv=cv, 
        scoring='accuracy', random_state=42, n_jobs=-1
    )
    
    random_search.fit(X_train, y_train)
    
    print("Random Forest hyperparameter tuning completed")
    print(f"Best parameters: {random_search.best_params_}")
    print(f"Best cross-validation score: {random_search.best_score_:.4f}")
    
    return random_search.best_estimator_


def hyperparameter_tuning_xgb(X_train, y_train, cv=3, n_iter=20):
    """
    Perform hyperparameter tuning for XGBoost
    
    Args:
        X_train (pd.DataFrame): Training features
        y_train (pd.Series): Training target
        cv (int): Cross-validation folds
        n_iter (int): Number of iterations for random search
        
    Returns:
        xgboost.XGBClassifier: Best model
    """
    param_dist = {
        'n_estimators': [50, 100, 200],
        'max_depth': [3, 4, 5, 6],
        'learning_rate': [0.01, 0.1, 0.2],
        'subsample': [0.8, 0.9, 1.0],
        'colsample_bytree': [0.8, 0.9, 1.0]
    }
    
    xgb_model = xgb.XGBClassifier(random_state=42, eval_metric='logloss')
    
    random_search = RandomizedSearchCV(
        xgb_model, param_dist, n_iter=n_iter, cv=cv,
        scoring='accuracy', random_state=42, n_jobs=-1
    )
    
    random_search.fit(X_train, y_train)
    
    print("XGBoost hyperparameter tuning completed")
    print(f"Best parameters: {random_search.best_params_}")
    print(f"Best cross-validation score: {random_search.best_score_:.4f}")
    
    return random_search.best_estimator_


def save_model(model, filepath):
    """
    Save trained model to file
    
    Args:
        model: Trained model
        filepath (str): Path to save the model
    """
    joblib.dump(model, filepath)
    print(f"Model saved to {filepath}")


def load_model(filepath):
    """
    Load trained model from file
    
    Args:
        filepath (str): Path to the saved model
        
    Returns:
        Loaded model
    """
    model = joblib.load(filepath)
    print(f"Model loaded from {filepath}")
    return model


def compare_models(models_dict, X_test, y_test):
    """
    Compare multiple models and return results
    
    Args:
        models_dict (dict): Dictionary of model_name: model pairs
        X_test (pd.DataFrame): Test features
        y_test (pd.Series): Test target
        
    Returns:
        pd.DataFrame: Comparison results
    """
    results = []
    
    for model_name, model in models_dict.items():
        metrics = evaluate_model(model, X_test, y_test, model_name)
        results.append(metrics)
    
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('accuracy', ascending=False)
    
    print("\n=== MODEL COMPARISON ===")
    print(results_df.round(4))
    
    return results_df
